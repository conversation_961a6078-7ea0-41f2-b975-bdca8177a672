﻿@page "/reviewrequests"
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Logging
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaWebApp.TeyaAIScribeResource.TeyaAIScribeResource> Localizer
@using TeyaWebApp.TeyaAIScribeResource
@using TeyaWebApp.Components.Layout
@layout Admin



<PageTitle>@Localizer["ReviewRequests"]</PageTitle>

<MudContainer MaxWidth="MaxWidth.False" Class="pa-6">
    <MudPaper Class="pa-6" Elevation="2" Style="border-radius: 8px;">
        <!-- Header Section -->
        <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween" Class="mb-4">
            <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                <MudIcon Icon="@Icons.Material.Filled.RateReview" Color="Color.Primary" Size="Size.Large" />
                <MudText Typo="Typo.h4" Style="font-weight: 600; color: #1976d2;">
                    @Localizer["ReviewRequests"]
                </MudText>
            </MudStack>
            <MudStack Row Spacing="2">
                <MudChip T="string" Color="Color.Warning" Size="Size.Medium" Icon="@Icons.Material.Filled.Schedule">
                    @Localizer["Pending"]: @_reviewRequests.Count(r => r.Status == CosigningRequestStatus.Pending)
                </MudChip>
                <MudChip T="string" Color="Color.Info" Size="Size.Medium" Icon="@Icons.Material.Filled.Info">
                    @Localizer["Total"]: @_reviewRequests.Count
                </MudChip>
            </MudStack>
        </MudStack>

        <MudDivider Class="mb-4" />

        <!-- Loading State -->
        @if (_isLoading)
        {
            <MudStack AlignItems="AlignItems.Center" Spacing="3" Class="pa-8">
                <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
                <MudText Typo="Typo.body1">@Localizer["LoadingRequests"]</MudText>
            </MudStack>
        }
        else if (!_reviewRequests.Any())
        {
            <!-- Empty State -->
            <MudStack AlignItems="AlignItems.Center" Spacing="3" Class="pa-8">
                <MudIcon Icon="@Icons.Material.Filled.RateReview" Size="Size.Large" Style="color: #9e9e9e;" />
                <MudText Typo="Typo.h6" Style="color: #9e9e9e;">@Localizer["NoReviewRequestsFound"]</MudText>
                <MudText Typo="Typo.body2" Style="color: #9e9e9e;">@Localizer["NoIncomingRequestsMessage"]</MudText>
            </MudStack>
        }
        else
        {
            <!-- Requests Table -->
            <MudTable Items="@_reviewRequests"
                      Hover="true"
                      Striped="true"
                      Dense="true"
                      FixedHeader="true"
                      Height="600px"
                      Class="requests-table">
                <HeaderContent>
                    <MudTh Style="font-weight: 600;">@Localizer["Patient"]</MudTh>
                    <MudTh Style="font-weight: 600;">@Localizer["Age"]</MudTh>
                    <MudTh Style="font-weight: 600;">@Localizer["Gender"]</MudTh>
                    <MudTh Style="font-weight: 600;">@Localizer["Requester"]</MudTh>
                    <MudTh Style="font-weight: 600;">@Localizer["Status"]</MudTh>
                    <MudTh Style="font-weight: 600;">@Localizer["RequestDate"]</MudTh>
                    <MudTh Style="font-weight: 600;">@Localizer["Actions"]</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="Patient">
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.Person" Size="Size.Small" Color="Color.Primary" />
                            <MudText Typo="Typo.body2" Style="font-weight: 500;">@context.PatientName</MudText>
                        </MudStack>
                    </MudTd>
                    <MudTd DataLabel="Age">@context.PatientAge</MudTd>
                    <MudTd DataLabel="Gender">@context.PatientGender</MudTd>
                    <MudTd DataLabel="Requester">
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.MedicalServices" Size="Size.Small" Color="Color.Secondary" />
                            <MudText Typo="Typo.body2">@context.RequesterName</MudText>
                        </MudStack>
                    </MudTd>
                    <MudTd DataLabel="Status">
                        <MudChip T="string"
                                 Color="@GetStatusColor(context.Status)"
                                 Size="Size.Small"
                                 Icon="@GetStatusIcon(context.Status)"
                                 Variant="Variant.Filled">
                            @GetStatusText(context.Status)
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="RequestDate">
                        <MudText Typo="Typo.body2">@context.RequestedDate.ToString("MM/dd/yyyy")</MudText>
                        <MudText Typo="Typo.caption" Class="text-muted">@context.RequestedDate.ToString("HH:mm")</MudText>
                    </MudTd>
                    <MudTd DataLabel="Actions">
                        <MudStack Row Spacing="1">
                            <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                           Color="Color.Primary"
                                           Size="Size.Small"
                                           OnClick="@(() => ViewNotes(context.RecordId))"
                                           Title="@Localizer["ViewNotes"]" />
                            @if (context.Status == CosigningRequestStatus.Pending)
                            {
                                <MudIconButton Icon="@Icons.Material.Filled.RateReview"
                                               Color="Color.Success"
                                               Size="Size.Small"
                                               OnClick="@(async () =>  OpenReviewDialog(context))"
                                               Title="@Localizer["Review"]" />
                            }
                            else if (context.Status == CosigningRequestStatus.ChangesRequested)
                            {
                                <MudIconButton Icon="@Icons.Material.Filled.Comment"
                                               Color="Color.Warning"
                                               Size="Size.Small"
                                               OnClick="@(() => ViewComments(context))"
                                               Title="@Localizer["ViewComments"]" />
                            }
                        </MudStack>
                    </MudTd>
                </RowTemplate>
            </MudTable>
        }
    </MudPaper>
</MudContainer>

<!-- Review Dialog -->
<MudDialog @bind-IsVisible="_showReviewDialog" Options="_dialogOptions">
    <TitleContent>
        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
            <MudIcon Icon="@Icons.Material.Filled.RateReview" Color="Color.Primary" />
            <MudText Typo="Typo.h6">@Localizer["ReviewRequest"]</MudText>
        </MudStack>
    </TitleContent>
    <DialogContent>
        @if (_selectedRequest != null)
        {
            <MudStack Spacing="3">
                <!-- Request Details -->
                <MudPaper Class="pa-4" Elevation="1" Style="background-color: #f5f5f5;">
                    <MudGrid>
                        <MudItem xs="6">
                            <MudText Typo="Typo.subtitle2" Style="font-weight: 600;">@Localizer["Patient"]:</MudText>
                            <MudText Typo="Typo.body2">@_selectedRequest.PatientName (@_selectedRequest.PatientAge, @_selectedRequest.PatientGender)</MudText>
                        </MudItem>
                        <MudItem xs="6">
                            <MudText Typo="Typo.subtitle2" Style="font-weight: 600;">@Localizer["Requester"]:</MudText>
                            <MudText Typo="Typo.body2">@_selectedRequest.RequesterName</MudText>
                        </MudItem>
                        <MudItem xs="12">
                            <MudText Typo="Typo.subtitle2" Style="font-weight: 600;">@Localizer["RequestDate"]:</MudText>
                            <MudText Typo="Typo.body2">@_selectedRequest.RequestedDate.ToString("MM/dd/yyyy HH:mm")</MudText>
                        </MudItem>
                    </MudGrid>
                </MudPaper>

                <!-- Comment Section for Changes Requested -->
                <MudTextField @bind-Value="_reviewComment"
                              Label="@Localizer["Comments"]"
                              Placeholder="@Localizer["EnterCommentsForChanges"]"
                              Lines="4"
                              Variant="Variant.Outlined"
                              HelperText="@Localizer["CommentsHelperText"]" />

                <!-- Action Buttons -->
                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                    <MudButton Variant="Variant.Text"
                               Color="Color.Default"
                               OnClick="CloseReviewDialog"
                               StartIcon="@Icons.Material.Filled.Cancel">
                        @Localizer["Cancel"]
                    </MudButton>

                    <MudStack Row Spacing="2">
                        <MudButton Variant="Variant.Filled"
                                   Color="Color.Warning"
                                   OnClick="RequestChanges"
                                   StartIcon="@Icons.Material.Filled.Comment"
                                   Disabled="@(string.IsNullOrWhiteSpace(_reviewComment))">
                            @Localizer["RequestChanges"]
                        </MudButton>

                        <MudButton Variant="Variant.Filled"
                                   Color="Color.Success"
                                   OnClick="ApproveRequest"
                                   StartIcon="@Icons.Material.Filled.CheckCircle">
                            @Localizer["Approve"]
                        </MudButton>
                    </MudStack>
                </MudStack>
            </MudStack>
        }
    </DialogContent>
</MudDialog>

<style>
    .requests-table {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .text-muted {
        color: #6c757d;
    }

    .mud-table-row:hover {
        background-color: rgba(25, 118, 210, 0.04) !important;
        cursor: pointer;
    }

    .mud-dialog {
        max-width: 600px;
    }
</style>