﻿using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.TeyaAIScribeResource;

namespace TeyaWebApp.Components.Pages
{
    /// <summary>
    /// Review Requests page for viewing and managing incoming co-signing requests
    /// </summary>
    public partial class Reviewrequests : ComponentBase
    {
        /// <summary>
        /// Current active user for authentication context
        /// </summary>
        [Inject] private ActiveUser CurrentUser { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        [Inject] private ICosigningRequestService CosigningRequestService { get; set; }
        [Inject] private ICosigningCommentHelper CommentHelper { get; set; }
        [Inject] private ILogger<Reviewrequests> Logger { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private NavigationManager Navigation { get; set; }

        /// <summary>
        /// List of incoming co-signing requests
        /// </summary>
        private List<CosigningRequest> _reviewRequests = new List<CosigningRequest>();

        /// <summary>
        /// Loading state indicator
        /// </summary>
        private bool _isLoading = true;

        /// <summary>
        /// Review dialog state
        /// </summary>
        private bool _showReviewDialog = false;
        private CosigningRequest? _selectedRequest = null;
        private string _reviewComment = string.Empty;

        /// <summary>
        /// Dialog options for review dialog
        /// </summary>
        private DialogOptions _dialogOptions = new()
        {
            CloseOnEscapeKey = true,
            MaxWidth = MaxWidth.Medium,
            FullWidth = true
        };

        /// <summary>
        /// Component initialization
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            try
            {
                await LoadReviewRequests();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error initializing ReviewRequests page");
                Snackbar.Add(Localizer["ErrorLoadingRequests"], Severity.Error);

            }
            finally
            {
                _isLoading = false;
                StateHasChanged();
            }
        }

        /// <summary>
        /// Load incoming co-signing requests for the current user
        /// </summary>
        private async Task LoadReviewRequests()
        {
            try
            {
                if (!Guid.TryParse(CurrentUser.id, out var userId))
                {
                    Logger.LogError("Invalid user ID: {UserId}", CurrentUser.id);
                    return;
                }

                var requests = await CosigningRequestService.GetByReviewerIdAsync(
                    userId,
                    UserContext.ActiveUserOrganizationID,
                    UserContext.ActiveUserSubscription
                );
                _reviewRequests = requests.ToList();

                Logger.LogInformation("Loaded {Count} incoming requests for user {UserId}", _reviewRequests.Count, userId);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading incoming requests for user {UserId}", CurrentUser.id);
                Snackbar.Add(Localizer["ErrorLoadingRequests"], Severity.Error);
            }
        }

        /// <summary>
        /// Get status color for display
        /// </summary>
        private Color GetStatusColor(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Pending => Color.Warning,
                CosigningRequestStatus.Approved => Color.Success,
                CosigningRequestStatus.ChangesRequested => Color.Error,
                _ => Color.Default
            };
        }

        /// <summary>
        /// Get status icon for display
        /// </summary>
        private string GetStatusIcon(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Pending => Icons.Material.Filled.Schedule,
                CosigningRequestStatus.Approved => Icons.Material.Filled.CheckCircle,
                CosigningRequestStatus.ChangesRequested => Icons.Material.Filled.Comment,
                _ => Icons.Material.Filled.Help
            };
        }

        /// <summary>
        /// Get status text for display
        /// </summary>
        private string GetStatusText(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Pending => Localizer["Pending"],
                CosigningRequestStatus.Approved => Localizer["Approved"],
                CosigningRequestStatus.ChangesRequested => Localizer["ChangesRequested"],
                _ => status.ToString()
            };
        }

        /// <summary>
        /// Navigate to notes page for the specified record
        /// </summary>
        private void ViewNotes(Guid recordId)
        {
            Navigation.NavigateTo($"/Chart?recordId={recordId}");
        }

        /// <summary>
        /// Open review dialog for a request
        /// </summary>
        private void OpenReviewDialog(CosigningRequest request)
        {
            _selectedRequest = request;
            _reviewComment = string.Empty;
            _showReviewDialog = true;
            StateHasChanged();
        }

        /// <summary>
        /// Close review dialog
        /// </summary>
        private void CloseReviewDialog()
        {
            _showReviewDialog = false;
            _selectedRequest = null;
            _reviewComment = string.Empty;
            StateHasChanged();
        }

        /// <summary>
        /// Approve the selected request
        /// </summary>
        private async Task ApproveRequest()
        {
            if (_selectedRequest == null) return;

            try
            {
                await CosigningRequestService.ApproveRequestAsync(
                    _selectedRequest.Id,
                    Guid.Parse(CurrentUser.id),
                    CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User",
                    UserContext.ActiveUserOrganizationID,
                    UserContext.ActiveUserSubscription
                );

                Snackbar.Add(Localizer["RequestApprovedSuccessfully"], Severity.Success);
                CloseReviewDialog();
                await LoadReviewRequests(); // Refresh the list
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error approving request {RequestId}", _selectedRequest.Id);
                Snackbar.Add(Localizer["ErrorApprovingRequest"], Severity.Error);
            }
        }

        /// <summary>
        /// Request changes for the selected request
        /// </summary>
        private async Task RequestChanges()
        {
            if (_selectedRequest == null || string.IsNullOrWhiteSpace(_reviewComment)) return;

            try
            {
                await CosigningRequestService.RequestChangesAsync(
                    _selectedRequest.Id,
                    Guid.Parse(CurrentUser.id),
                    CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User",
                    _reviewComment,
                    UserContext.ActiveUserOrganizationID,
                    UserContext.ActiveUserSubscription
                );

                Snackbar.Add(Localizer["ChangesRequestedSuccessfully"], Severity.Success);
                CloseReviewDialog();
                await LoadReviewRequests(); // Refresh the list
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error requesting changes for request {RequestId}", _selectedRequest.Id);
                Snackbar.Add(Localizer["ErrorRequestingChanges"], Severity.Error);
            }
        }

        /// <summary>
        /// View comments for a request with changes requested
        /// </summary>
        private async Task ViewComments(CosigningRequest request)
        {
            try
            {
                // Get comments from the request
                var comments = CommentHelper.GetComments(request.CommentsJson);

                if (comments.Any())
                {
                    var commentsText = string.Join("\n\n", comments.Select(c =>
                        $"[{c.CommentDate:MM/dd/yyyy HH:mm}] {c.CommenterName}: {c.Comment}"));

                    await DialogService.ShowMessageBox(
                        Localizer["Comments"],
                        commentsText,
                        yesText: Localizer["Close"]
                    );
                }
                else
                {
                    Snackbar.Add(Localizer["NoCommentsFound"], Severity.Info);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error viewing comments for request {RequestId}", request.Id);
                Snackbar.Add(Localizer["ErrorViewingComments"], Severity.Error);
            }
        }
    }
}